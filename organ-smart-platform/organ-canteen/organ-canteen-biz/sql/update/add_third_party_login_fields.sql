-- 为智慧食堂用户表添加第三方登录相关字段
-- 执行时间：2024-01-XX

-- 添加工号字段（用于匹配第三方用户）
ALTER TABLE canteen_user ADD COLUMN IF NOT EXISTS employee_no varchar(50);
COMMENT ON COLUMN canteen_user.employee_no IS '工号（用于匹配第三方用户）';

-- 添加邮箱字段（用于匹配第三方用户）
ALTER TABLE canteen_user ADD COLUMN IF NOT EXISTS email varchar(100);
COMMENT ON COLUMN canteen_user.email IS '邮箱（用于匹配第三方用户）';

-- 添加真实姓名字段（用于匹配第三方用户）
ALTER TABLE canteen_user ADD COLUMN IF NOT EXISTS real_name varchar(100);
COMMENT ON COLUMN canteen_user.real_name IS '真实姓名（用于匹配第三方用户）';

-- 添加头像字段
ALTER TABLE canteen_user ADD COLUMN IF NOT EXISTS avatar varchar(500);
COMMENT ON COLUMN canteen_user.avatar IS '头像';

-- 添加部门ID字段（用于匹配第三方用户）
ALTER TABLE canteen_user ADD COLUMN IF NOT EXISTS dept_id bigint;
COMMENT ON COLUMN canteen_user.dept_id IS '部门ID（用于匹配第三方用户）';

-- 添加部门名称字段（用于匹配第三方用户）
ALTER TABLE canteen_user ADD COLUMN IF NOT EXISTS dept_name varchar(200);
COMMENT ON COLUMN canteen_user.dept_name IS '部门名称（用于匹配第三方用户）';

-- 添加身份证号字段（用于匹配第三方用户）
ALTER TABLE canteen_user ADD COLUMN IF NOT EXISTS id_card varchar(18);
COMMENT ON COLUMN canteen_user.id_card IS '身份证号（用于匹配第三方用户）';

-- 添加第三方用户ID字段（用于关联第三方用户）
ALTER TABLE canteen_user ADD COLUMN IF NOT EXISTS third_party_user_id varchar(100);
COMMENT ON COLUMN canteen_user.third_party_user_id IS '第三方用户ID（用于关联第三方用户）';

-- 添加消费总额字段
ALTER TABLE canteen_user ADD COLUMN IF NOT EXISTS total_consumption decimal(10,2) DEFAULT 0.00;
COMMENT ON COLUMN canteen_user.total_consumption IS '消费总额';

-- 添加会员等级字段
ALTER TABLE canteen_user ADD COLUMN IF NOT EXISTS member_level varchar(50) DEFAULT '普通会员';
COMMENT ON COLUMN canteen_user.member_level IS '会员等级';

-- 添加最后登录时间字段
ALTER TABLE canteen_user ADD COLUMN IF NOT EXISTS last_login_time timestamp;
COMMENT ON COLUMN canteen_user.last_login_time IS '最后登录时间';

-- 添加最后消费时间字段
ALTER TABLE canteen_user ADD COLUMN IF NOT EXISTS last_consumption_time timestamp;
COMMENT ON COLUMN canteen_user.last_consumption_time IS '最后消费时间';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_canteen_user_third_party_user_id ON canteen_user(third_party_user_id);
CREATE INDEX IF NOT EXISTS idx_canteen_user_employee_no ON canteen_user(employee_no);
CREATE INDEX IF NOT EXISTS idx_canteen_user_mobile ON canteen_user(mobile);
CREATE INDEX IF NOT EXISTS idx_canteen_user_id_card ON canteen_user(id_card);

-- 添加唯一约束（如果需要）
-- ALTER TABLE canteen_user ADD CONSTRAINT uk_canteen_user_third_party_user_id UNIQUE (third_party_user_id);
-- ALTER TABLE canteen_user ADD CONSTRAINT uk_canteen_user_employee_no UNIQUE (employee_no);
