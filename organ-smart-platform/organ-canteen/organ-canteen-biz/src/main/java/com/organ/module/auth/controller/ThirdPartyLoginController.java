package com.organ.module.auth.controller;

import com.hainancrc.framework.common.pojo.CommonResult;
import com.organ.module.auth.dto.ThirdPartyLoginDTO;
import com.organ.module.auth.service.ThirdPartyLoginService;
import com.organ.module.auth.vo.ThirdPartyLoginVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

/**
 * 第三方登录控制器
 *
 * <AUTHOR>
 */
@Api(tags = "第三方登录")
@RestController
@RequestMapping("/auth")
@Slf4j
public class ThirdPartyLoginController {

    @Resource
    private ThirdPartyLoginService thirdPartyLoginService;

    /**
     * 通过accessToken进行第三方登录
     *
     * @param loginDTO 登录请求参数
     * @return 登录结果
     */
    @PostMapping("/loginByAccessToken")
    @ApiOperation("通过accessToken进行第三方登录")
    public CommonResult<ThirdPartyLoginVO> loginByAccessToken(@Valid @RequestBody ThirdPartyLoginDTO loginDTO) {
        log.info("收到第三方登录请求，clientType: {}", loginDTO.getClientType());
        
        ThirdPartyLoginVO result = thirdPartyLoginService.loginByAccessToken(loginDTO);
        
        log.info("第三方登录成功，用户ID: {}", result.getUserInfo().getId());
        return success(result);
    }

}
