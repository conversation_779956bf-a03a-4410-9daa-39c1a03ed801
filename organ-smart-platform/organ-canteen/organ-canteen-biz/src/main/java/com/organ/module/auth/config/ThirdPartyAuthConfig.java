package com.organ.module.auth.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 第三方认证配置类
 * 用于管理不同客户端的AK、SK配置信息
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "third-party.auth")
@Data
public class ThirdPartyAuthConfig {

    /**
     * 第三方认证服务基础URL
     */
    private String baseUrl;

    /**
     * 获取token的API路径
     */
    private String tokenApiPath = "/api/auth/token";

    /**
     * 获取用户信息的API路径
     */
    private String userInfoApiPath = "/api/auth/userinfo";

    /**
     * 不同客户端的配置信息
     * key: 客户端类型（user、operation、admin）
     * value: 客户端配置信息
     */
    private Map<String, ClientConfig> clients;

    /**
     * 客户端配置信息
     */
    @Data
    public static class ClientConfig {
        /**
         * 应用标识（AK）
         */
        private String appKey;

        /**
         * 应用密钥（SK）
         */
        private String appSecret;

        /**
         * 客户端名称
         */
        private String clientName;

        /**
         * 是否启用
         */
        private Boolean enabled = true;
    }

    /**
     * 根据客户端类型获取配置信息
     *
     * @param clientType 客户端类型
     * @return 客户端配置信息
     */
    public ClientConfig getClientConfig(String clientType) {
        if (clients == null) {
            return null;
        }
        return clients.get(clientType);
    }

    /**
     * 获取完整的token API URL
     *
     * @return token API URL
     */
    public String getTokenApiUrl() {
        return baseUrl + tokenApiPath;
    }

    /**
     * 获取完整的用户信息API URL
     *
     * @return 用户信息API URL
     */
    public String getUserInfoApiUrl() {
        return baseUrl + userInfoApiPath;
    }

}
