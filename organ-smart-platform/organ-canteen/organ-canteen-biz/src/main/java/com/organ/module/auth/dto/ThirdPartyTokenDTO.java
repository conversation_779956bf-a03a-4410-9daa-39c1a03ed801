package com.organ.module.auth.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 第三方token信息DTO
 *
 * <AUTHOR>
 */
@ApiModel("第三方token信息")
@Data
public class ThirdPartyTokenDTO {

    /**
     * 第三方系统token
     */
    @ApiModelProperty("第三方系统token")
    private String token;

    /**
     * 刷新token
     */
    @ApiModelProperty("刷新token")
    private String refreshToken;

    /**
     * token过期时间（秒）
     */
    @ApiModelProperty("token过期时间（秒）")
    private Long expiresIn;

    /**
     * token类型
     */
    @ApiModelProperty("token类型")
    private String tokenType;

}
