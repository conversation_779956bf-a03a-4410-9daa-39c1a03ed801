package com.organ.module.auth.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 第三方token信息DTO
 *
 * <AUTHOR>
 */
@ApiModel("第三方token信息")
@Data
public class ThirdPartyTokenDTO {

    /**
     * 访问token
     */
    @ApiModelProperty("访问token")
    private String accessToken;

    /**
     * token过期时间（秒）
     */
    @ApiModelProperty("token过期时间（秒）")
    private Long expiredIn;

    /**
     * 刷新token
     */
    @ApiModelProperty("刷新token")
    private String refreshToken;

}
