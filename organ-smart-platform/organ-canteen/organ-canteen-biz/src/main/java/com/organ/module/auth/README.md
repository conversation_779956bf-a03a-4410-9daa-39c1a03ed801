# 第三方登录功能说明

## 功能概述

本模块实现了智慧食堂系统与第三方统一认证系统（海政通）的对接，支持用户通过第三方认证中心登录智慧食堂系统。

## 登录流程

1. **用户端发起登录** - 用户在前端点击登录按钮
2. **跳转第三方认证** - 系统跳转到第三方统一认证登录中心
3. **认证成功返回** - 认证中心登录成功后通过query参数返回accessToken
4. **前端调用登录接口** - 前端拿到accessToken后调用`/auth/loginByAccessToken`接口
5. **后端处理登录** - 后端通过以下步骤完成登录：
   - 通过SK+accessToken获取第三方系统token
   - 通过accessToken+Token获取第三方用户信息
   - 匹配智慧食堂用户信息
   - 生成系统token并存储到Redis
   - 返回登录结果

## 接口说明

### 登录接口

**接口地址：** `POST /auth/loginByAccessToken`

**请求参数：**
```json
{
  "accessToken": "第三方认证中心返回的accessToken",
  "clientType": "客户端类型（user/operation/admin）"
}
```

**响应结果：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "token": "系统生成的token",
    "refreshToken": "第三方刷新token",
    "userInfo": {
      "id": 1,
      "username": "张三",
      "realName": "张三",
      "email": "<EMAIL>",
      "phone": "13800138000",
      "avatar": "头像URL",
      "employeeNo": "EMP001",
      "deptId": 1,
      "deptName": "研发部",
      "status": 1,
      "balance": 100.0,
      "totalConsumption": 500.0,
      "memberLevel": "VIP"
    },
    "expiresIn": 3600
  }
}
```

## 配置说明

### 应用配置

在`application-dev.yaml`中配置第三方认证信息：

```yaml
third-party:
  auth:
    base-url: https://open-hzt-api.digitalhainan.com.cn
    token-api-path: /api/auth/token
    user-info-api-path: /api/auth/userinfo
    clients:
      user:
        app-key: ${THIRD_PARTY_USER_AK:your-user-app-key}
        app-secret: ${THIRD_PARTY_USER_SK:your-user-app-secret}
        client-name: 用户端
        enabled: true
      operation:
        app-key: ${THIRD_PARTY_OPERATION_AK:your-operation-app-key}
        app-secret: ${THIRD_PARTY_OPERATION_SK:your-operation-app-secret}
        client-name: 运营端
        enabled: true
      admin:
        app-key: ${THIRD_PARTY_ADMIN_AK:your-admin-app-key}
        app-secret: ${THIRD_PARTY_ADMIN_SK:your-admin-app-secret}
        client-name: 管理端
        enabled: true
```

### 环境变量配置

需要设置以下环境变量：
- `THIRD_PARTY_USER_AK`: 用户端应用标识
- `THIRD_PARTY_USER_SK`: 用户端应用密钥
- `THIRD_PARTY_OPERATION_AK`: 运营端应用标识
- `THIRD_PARTY_OPERATION_SK`: 运营端应用密钥
- `THIRD_PARTY_ADMIN_AK`: 管理端应用标识
- `THIRD_PARTY_ADMIN_SK`: 管理端应用密钥

## 用户匹配规则

系统按以下优先级匹配第三方用户与智慧食堂用户：

1. **第三方用户ID匹配** - 优先通过已关联的第三方用户ID匹配
2. **工号匹配** - 通过工号匹配用户
3. **手机号匹配** - 通过手机号匹配用户
4. **身份证号匹配** - 通过身份证号匹配用户
5. **创建新用户** - 如果都匹配不到，则创建新用户

## 工具类使用

### SystemUserUtil工具类

提供获取当前登录用户信息的静态方法：

```java
// 获取当前登录用户信息
SystemUserInfoVO currentUser = SystemUserUtil.getCurrentUser();

// 获取当前用户ID
Long userId = SystemUserUtil.getCurrentUserId();

// 获取当前用户名
String username = SystemUserUtil.getCurrentUsername();

// 检查用户是否已登录
boolean isLoggedIn = SystemUserUtil.isLoggedIn();

// 强制从第三方API刷新用户信息
SystemUserInfoVO refreshedUser = SystemUserUtil.getCurrentUser(true);
```

## 数据库变更

执行以下SQL脚本为用户表添加第三方登录相关字段：

```sql
-- 执行 sql/update/add_third_party_login_fields.sql
```

## 注意事项

1. **Token过期处理** - 当Redis中的token过期时，系统会返回401状态码，前端需要重新登录
2. **用户信息刷新** - 每次调用接口时会自动刷新Redis中的过期时间
3. **异常处理** - 所有第三方API调用失败都会抛出ServiceException，前端需要适当处理
4. **并发安全** - 用户匹配和创建过程使用了事务保证数据一致性

## 错误码说明

- `401` - 用户未登录或token已过期
- `500` - 系统内部错误
- 其他业务错误码根据具体异常情况返回

## 开发调试

1. 确保第三方API服务可访问
2. 配置正确的AK、SK
3. 检查Redis连接是否正常
4. 查看日志了解详细的执行过程
