package com.organ.module.canteen.user.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 智慧食堂用户信息实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("canteen_user")
@EqualsAndHashCode(callSuper = true)
public class CanteenUserDO extends BaseDO {

    /**
     * 用户ID
     */
    @TableId
    private Long id;

    /**
     * 用户姓名
     */
    private String username;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 用户所属单位
     */
    private String dept;

    /**
     * 就餐食堂id（原开卡食堂，关联食堂信息表）
     */
    private Long canteenId;

    /**
     * 就餐食堂编码（原开卡食堂，关联食堂信息表）
     */
    private String canteenCode;

    /**
     * 用户卡里余额（预留）
     */
    private BigDecimal balance;

    /**
     * 用户持卡类别（1-一类 2-二类 3-三类）
     */
    private String cardType;

    /**
     * 用户类型（REGULAR-编内 CONTRACT-编外）
     */
    private String userType;

    /**
     * 卡过期时间（不设置时就是永久有效）
     */
    private LocalDateTime cardExpireTime;

    /**
     * 是否允许跨区域用餐（0-不允许 1-允许）
     */
    private Integer crossRegionFlag;

    /**
     * 就餐区域（关联字典表 字典 dining_region）
     */
    private String diningRegion;

    /**
     * 用户状态（ACTIVE-正常 DISABLED-禁用）
     */
    private String status;

    /**
     * 工号（用于匹配第三方用户）
     */
    private String employeeNo;

    /**
     * 邮箱（用于匹配第三方用户）
     */
    private String email;

    /**
     * 真实姓名（用于匹配第三方用户）
     */
    private String realName;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 部门ID（用于匹配第三方用户）
     */
    private Long deptId;

    /**
     * 部门名称（用于匹配第三方用户）
     */
    private String deptName;

    /**
     * 身份证号（用于匹配第三方用户）
     */
    private String idCard;

    /**
     * 第三方用户ID（用于关联第三方用户）
     */
    private String thirdPartyUserId;

    /**
     * 消费总额
     */
    private BigDecimal totalConsumption;

    /**
     * 会员等级
     */
    private String memberLevel;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 最后消费时间
     */
    private LocalDateTime lastConsumptionTime;

}
