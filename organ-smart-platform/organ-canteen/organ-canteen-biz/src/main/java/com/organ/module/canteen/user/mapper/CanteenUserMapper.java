package com.organ.module.canteen.user.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.organ.module.canteen.user.entity.CanteenUserDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 智慧食堂用户 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CanteenUserMapper extends BaseMapperX<CanteenUserDO> {

    /**
     * 根据第三方用户数据查询用户（通过JSON字符串中的userId匹配）
     *
     * @param userId 第三方用户ID
     * @return 用户信息
     */
    default CanteenUserDO selectByThirdPartyUserId(String userId) {
        return selectOne(new LambdaQueryWrapper<CanteenUserDO>()
                .like(CanteenUserDO::getThirdPartyUserData, "\"userId\":\"" + userId + "\"")
                .eq(CanteenUserDO::getDeleted, 0));
    }

    /**
     * 根据手机号查询用户
     *
     * @param mobile 手机号
     * @return 用户信息
     */
    default CanteenUserDO selectByMobile(String mobile) {
        return selectOne(new LambdaQueryWrapper<CanteenUserDO>()
                .eq(CanteenUserDO::getMobile, mobile)
                .eq(CanteenUserDO::getDeleted, 0));
    }

}
