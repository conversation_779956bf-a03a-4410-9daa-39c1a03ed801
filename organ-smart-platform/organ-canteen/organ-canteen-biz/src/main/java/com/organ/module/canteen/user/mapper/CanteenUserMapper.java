package com.organ.module.canteen.user.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.organ.module.canteen.user.entity.CanteenUserDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 智慧食堂用户 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CanteenUserMapper extends BaseMapperX<CanteenUserDO> {

    /**
     * 根据第三方用户ID查询用户
     *
     * @param thirdPartyUserId 第三方用户ID
     * @return 用户信息
     */
    default CanteenUserDO selectByThirdPartyUserId(String thirdPartyUserId) {
        return selectOne(new LambdaQueryWrapper<CanteenUserDO>()
                .eq(CanteenUserDO::getThirdPartyUserId, thirdPartyUserId)
                .eq(CanteenUserDO::getDeleted, 0));
    }

    /**
     * 根据工号查询用户
     *
     * @param employeeNo 工号
     * @return 用户信息
     */
    default CanteenUserDO selectByEmployeeNo(String employeeNo) {
        return selectOne(new LambdaQueryWrapper<CanteenUserDO>()
                .eq(CanteenUserDO::getEmployeeNo, employeeNo)
                .eq(CanteenUserDO::getDeleted, 0));
    }

    /**
     * 根据手机号查询用户
     *
     * @param mobile 手机号
     * @return 用户信息
     */
    default CanteenUserDO selectByMobile(String mobile) {
        return selectOne(new LambdaQueryWrapper<CanteenUserDO>()
                .eq(CanteenUserDO::getMobile, mobile)
                .eq(CanteenUserDO::getDeleted, 0));
    }

    /**
     * 根据身份证号查询用户
     *
     * @param idCard 身份证号
     * @return 用户信息
     */
    default CanteenUserDO selectByIdCard(String idCard) {
        return selectOne(new LambdaQueryWrapper<CanteenUserDO>()
                .eq(CanteenUserDO::getIdCard, idCard)
                .eq(CanteenUserDO::getDeleted, 0));
    }

}
