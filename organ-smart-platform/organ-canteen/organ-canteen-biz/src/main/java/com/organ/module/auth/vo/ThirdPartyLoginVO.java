package com.organ.module.auth.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 第三方登录响应VO
 *
 * <AUTHOR>
 */
@ApiModel("第三方登录响应")
@Data
public class ThirdPartyLoginVO {

    /**
     * 系统生成的token
     */
    @ApiModelProperty("系统生成的token")
    private String token;

    /**
     * 第三方刷新token
     */
    @ApiModelProperty("第三方刷新token")
    private String refreshToken;

    /**
     * 智慧食堂用户信息
     */
    @ApiModelProperty("智慧食堂用户信息")
    private SystemUserInfoVO userInfo;

    /**
     * token过期时间（秒）
     */
    @ApiModelProperty("token过期时间（秒）")
    private Long expiresIn;

}
