# 第三方登录说明

## 登录流程

1. 用户端发起登录
2. 跳转第三方统一认证登录中心登录
3. 认证中心登录成功后通过 query accessToken 的方式返回前端对应的页面
4. 前端拿到对应 accessToken 后通过调用 loginByAccessToken 接口进行登录
5. 后端 loginByAccessToken 对接第三方登录系统后端进行登录
   1. 通过 SK+accessToken 调用第三方提供的获取登录用户 token 开放 API，获取登录 token，refreshToken，expiredIn(主 token 过期时间，单位秒)
   2. 通过 accessToken + Token 调用第三方提供的获取登录用户信息开放 API，获取第三方登录用户信息 hztUserInfo
   3. 通过一些列匹配措施（单独写一个匹配方法），将 hztUerInfo 对应到当前智慧食堂的用户信息 systemUserInfo
   4. 将 “canteenRk-” + token: systemUserInfo 键值对存储到 redis 中，过期时间设置为 expiredIn，每次调用接口时，更新刷新对应的过期时间
   5. 将对应的 token、refreshToken、systemUserInfo 返回给前端

## 功能需求

1. 实现用户登录的后端逻辑
2. 提供一个 SystemUserUtil 工具类
   1. 该工具类提供一个获取当前登录用户的方法，返回值是对应的智慧食堂的用户信息
      1. 优先从 redis 中获取，如果没有的通过第三方提供的开放 API 获取
      2. 如果提示过期的返回 401 给前端提示重新登录
   2. SystemUserUtil 放在 common 文件夹下
   3. 如果已过期的话提示前端重新登录

## AK、SK 说明

1. AK 和 SK 会通过配置给出
2. 来自不同端（用户端、运营端、管理端）的请求会有不同的 AK 和 SK

## 对接文档参考

[服务端开放 API](https://open-hzt-helpdoc.digitalhainan.com.cn/docs/%E7%BB%9F%E4%B8%80%E7%94%A8%E6%88%B7%E5%AF%B9%E6%8E%A5/%E6%9C%8D%E5%8A%A1%E7%AB%AFAPI/%E7%99%BB%E5%BD%95%E8%AE%A4%E8%AF%81%E7%AE%A1%E7%90%86.html)
