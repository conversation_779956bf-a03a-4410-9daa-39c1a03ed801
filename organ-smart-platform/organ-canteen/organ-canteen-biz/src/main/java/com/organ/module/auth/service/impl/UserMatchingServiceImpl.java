package com.organ.module.auth.service.impl;

import cn.hutool.core.util.StrUtil;
import com.hainancrc.framework.common.exception.ServiceException;
import com.organ.module.auth.dto.HztUserInfoDTO;
import com.organ.module.auth.service.UserMatchingService;
import com.organ.module.auth.vo.SystemUserInfoVO;
import com.organ.module.canteen.user.entity.CanteenUserDO;
import com.organ.module.canteen.user.mapper.CanteenUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户匹配服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserMatchingServiceImpl implements UserMatchingService {

    @Resource
    private CanteenUserMapper canteenUserMapper;

    @Override
    public SystemUserInfoVO matchUser(HztUserInfoDTO hztUserInfo) {
        log.info("开始匹配用户，第三方用户ID: {}, 用户名: {}", hztUserInfo.getUserId(), hztUserInfo.getUsername());
        
        CanteenUserDO canteenUser = null;
        
        // 1. 优先通过第三方用户ID匹配
        if (StrUtil.isNotBlank(hztUserInfo.getUserId())) {
            canteenUser = canteenUserMapper.selectByThirdPartyUserId(hztUserInfo.getUserId());
            if (canteenUser != null) {
                log.info("通过第三方用户ID匹配到用户: {}", canteenUser.getId());
                return convertToSystemUserInfo(canteenUser);
            }
        }
        
        // 2. 通过工号匹配
        if (StrUtil.isNotBlank(hztUserInfo.getEmployeeNo())) {
            canteenUser = canteenUserMapper.selectByEmployeeNo(hztUserInfo.getEmployeeNo());
            if (canteenUser != null) {
                log.info("通过工号匹配到用户: {}", canteenUser.getId());
                // 更新第三方关联信息
                updateUserThirdPartyInfo(canteenUser.getId(), hztUserInfo.getUserId(), hztUserInfo);
                return convertToSystemUserInfo(canteenUser);
            }
        }
        
        // 3. 通过手机号匹配
        if (StrUtil.isNotBlank(hztUserInfo.getPhone())) {
            canteenUser = canteenUserMapper.selectByMobile(hztUserInfo.getPhone());
            if (canteenUser != null) {
                log.info("通过手机号匹配到用户: {}", canteenUser.getId());
                // 更新第三方关联信息
                updateUserThirdPartyInfo(canteenUser.getId(), hztUserInfo.getUserId(), hztUserInfo);
                return convertToSystemUserInfo(canteenUser);
            }
        }
        
        // 4. 通过身份证号匹配
        if (StrUtil.isNotBlank(hztUserInfo.getIdCard())) {
            canteenUser = canteenUserMapper.selectByIdCard(hztUserInfo.getIdCard());
            if (canteenUser != null) {
                log.info("通过身份证号匹配到用户: {}", canteenUser.getId());
                // 更新第三方关联信息
                updateUserThirdPartyInfo(canteenUser.getId(), hztUserInfo.getUserId(), hztUserInfo);
                return convertToSystemUserInfo(canteenUser);
            }
        }
        
        // 5. 如果都匹配不到，则创建新用户
        log.info("未匹配到现有用户，创建新用户");
        canteenUser = createNewUser(hztUserInfo);
        return convertToSystemUserInfo(canteenUser);
    }

    @Override
    public void updateUserThirdPartyInfo(Long userId, String thirdPartyUserId, HztUserInfoDTO hztUserInfo) {
        log.info("更新用户第三方关联信息，用户ID: {}, 第三方用户ID: {}", userId, thirdPartyUserId);
        
        CanteenUserDO updateUser = new CanteenUserDO();
        updateUser.setId(userId);
        updateUser.setThirdPartyUserId(thirdPartyUserId);
        
        // 更新用户信息
        if (StrUtil.isNotBlank(hztUserInfo.getRealName())) {
            updateUser.setRealName(hztUserInfo.getRealName());
        }
        if (StrUtil.isNotBlank(hztUserInfo.getEmail())) {
            updateUser.setEmail(hztUserInfo.getEmail());
        }
        if (StrUtil.isNotBlank(hztUserInfo.getAvatar())) {
            updateUser.setAvatar(hztUserInfo.getAvatar());
        }
        if (StrUtil.isNotBlank(hztUserInfo.getDeptName())) {
            updateUser.setDeptName(hztUserInfo.getDeptName());
        }
        if (StrUtil.isNotBlank(hztUserInfo.getDeptId())) {
            try {
                updateUser.setDeptId(Long.valueOf(hztUserInfo.getDeptId()));
            } catch (NumberFormatException e) {
                log.warn("第三方部门ID格式错误: {}", hztUserInfo.getDeptId());
            }
        }
        
        updateUser.setLastLoginTime(LocalDateTime.now());
        
        canteenUserMapper.updateById(updateUser);
    }

    /**
     * 创建新用户
     *
     * @param hztUserInfo 第三方用户信息
     * @return 新创建的用户
     */
    private CanteenUserDO createNewUser(HztUserInfoDTO hztUserInfo) {
        CanteenUserDO newUser = new CanteenUserDO();
        
        // 基本信息
        newUser.setUsername(StrUtil.isNotBlank(hztUserInfo.getRealName()) ? hztUserInfo.getRealName() : hztUserInfo.getUsername());
        newUser.setRealName(hztUserInfo.getRealName());
        newUser.setMobile(hztUserInfo.getPhone());
        newUser.setEmail(hztUserInfo.getEmail());
        newUser.setEmployeeNo(hztUserInfo.getEmployeeNo());
        newUser.setIdCard(hztUserInfo.getIdCard());
        newUser.setAvatar(hztUserInfo.getAvatar());
        newUser.setThirdPartyUserId(hztUserInfo.getUserId());
        
        // 部门信息
        newUser.setDept(hztUserInfo.getDeptName());
        newUser.setDeptName(hztUserInfo.getDeptName());
        if (StrUtil.isNotBlank(hztUserInfo.getDeptId())) {
            try {
                newUser.setDeptId(Long.valueOf(hztUserInfo.getDeptId()));
            } catch (NumberFormatException e) {
                log.warn("第三方部门ID格式错误: {}", hztUserInfo.getDeptId());
            }
        }
        
        // 默认值设置
        newUser.setCanteenId(1L); // 默认食堂ID，可根据实际情况调整
        newUser.setCanteenCode("DEFAULT"); // 默认食堂编码，可根据实际情况调整
        newUser.setBalance(BigDecimal.ZERO);
        newUser.setCardType("1"); // 默认一类卡
        newUser.setUserType("REGULAR"); // 默认编内用户
        newUser.setCrossRegionFlag(1); // 默认允许跨区域用餐
        newUser.setDiningRegion("A区"); // 默认就餐区域
        newUser.setStatus("ACTIVE"); // 默认激活状态
        newUser.setTotalConsumption(BigDecimal.ZERO);
        newUser.setMemberLevel("普通会员");
        newUser.setLastLoginTime(LocalDateTime.now());
        
        try {
            canteenUserMapper.insert(newUser);
            log.info("成功创建新用户，用户ID: {}", newUser.getId());
            return newUser;
        } catch (Exception e) {
            log.error("创建新用户失败", e);
            throw new ServiceException("创建用户失败: " + e.getMessage());
        }
    }

    /**
     * 将CanteenUserDO转换为SystemUserInfoVO
     *
     * @param canteenUser 智慧食堂用户实体
     * @return 系统用户信息VO
     */
    private SystemUserInfoVO convertToSystemUserInfo(CanteenUserDO canteenUser) {
        SystemUserInfoVO userInfo = new SystemUserInfoVO();
        
        userInfo.setId(canteenUser.getId());
        userInfo.setUsername(canteenUser.getUsername());
        userInfo.setRealName(canteenUser.getRealName());
        userInfo.setEmail(canteenUser.getEmail());
        userInfo.setPhone(canteenUser.getMobile());
        userInfo.setAvatar(canteenUser.getAvatar());
        userInfo.setEmployeeNo(canteenUser.getEmployeeNo());
        userInfo.setDeptId(canteenUser.getDeptId());
        userInfo.setDeptName(canteenUser.getDeptName());
        
        // 状态转换
        if ("ACTIVE".equals(canteenUser.getStatus())) {
            userInfo.setStatus(1);
        } else {
            userInfo.setStatus(0);
        }
        
        userInfo.setBalance(canteenUser.getBalance() != null ? canteenUser.getBalance().doubleValue() : 0.0);
        userInfo.setTotalConsumption(canteenUser.getTotalConsumption() != null ? canteenUser.getTotalConsumption().doubleValue() : 0.0);
        userInfo.setMemberLevel(canteenUser.getMemberLevel());
        
        return userInfo;
    }

}
