package com.organ.module.auth.util;

import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;

import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.Map;
import java.util.TreeMap;

/**
 * HTTP调用工具类
 * 模拟第三方API调用方式
 *
 * <AUTHOR>
 */
@Slf4j
public class HttpCaller {

    private static final HttpCaller INSTANCE = new HttpCaller();

    private HttpCaller() {
    }

    public static HttpCaller getInstance() {
        return INSTANCE;
    }

    /**
     * 调用第三方API
     *
     * @param parameters HTTP请求参数
     * @return HTTP响应结果
     */
    public HttpReturn call(HttpParameters parameters) {
        try {
            // 构建签名
            String signature = buildSignature(parameters);

            // 构建请求URL
            String url = parameters.getRequestUrl();

            // 发送HTTP请求
            HttpRequest request = HttpRequest.post(url)
                    .body(parameters.getContentBody().getContent())
                    .contentType("application/json");

            // 添加请求头
            if (parameters.getHeaderParamsMap() != null) {
                for (Map.Entry<String, String> entry : parameters.getHeaderParamsMap().entrySet()) {
                    request.header(entry.getKey(), entry.getValue());
                }
            }

            // 添加签名相关的请求头
            request.header("X-Ca-Key", parameters.getAccessKey());
            request.header("X-Ca-Signature", signature);
            request.header("X-Ca-Timestamp", String.valueOf(Instant.now().toEpochMilli()));
            request.header("X-Ca-Nonce", String.valueOf(System.nanoTime()));
            request.header("X-Ca-Signature-Method", "HmacSHA256");

            log.info("发送HTTP请求，URL: {}, API: {}", url, parameters.getApi());

            HttpResponse response = request.execute();

            log.info("收到HTTP响应，状态码: {}", response.getStatus());

            return new HttpReturn(response.getStatus(), response.body());

        } catch (Exception e) {
            log.error("HTTP调用异常", e);
            return new HttpReturn(500, "{\"success\":false,\"message\":\"HTTP调用异常: " + e.getMessage() + "\"}");
        }
    }

    /**
     * 构建签名
     *
     * @param parameters 请求参数
     * @return 签名字符串
     */
    private String buildSignature(HttpParameters parameters) {
        try {
            // 构建待签名字符串
            TreeMap<String, String> signParams = new TreeMap<>();
            signParams.put("api", parameters.getApi());
            signParams.put("region", parameters.getRegion());
            signParams.put("accessKey", parameters.getAccessKey());

            StringBuilder signString = new StringBuilder();
            for (Map.Entry<String, String> entry : signParams.entrySet()) {
                if (signString.length() > 0) {
                    signString.append("&");
                }
                signString.append(entry.getKey()).append("=").append(entry.getValue());
            }

            // 添加请求体
            if (parameters.getContentBody() != null && parameters.getContentBody().getContent() != null) {
                signString.append("&body=").append(parameters.getContentBody().getContent());
            }

            log.debug("待签名字符串: {}", signString.toString());

            // 使用HMAC-SHA256进行签名
            HMac hMac = new HMac(HmacAlgorithm.HmacSHA256, parameters.getSecretKey().getBytes(StandardCharsets.UTF_8));
            return hMac.digestHex(signString.toString());

        } catch (Exception e) {
            log.error("构建签名失败", e);
            throw new RuntimeException("构建签名失败", e);
        }
    }

    /**
     * HTTP请求参数
     */
    public static class HttpParameters {
        private String api;
        private String region;
        private String accessKey;
        private String secretKey;
        private ContentBody contentBody;
        private String requestUrl;
        private Map<String, String> headerParamsMap;

        private HttpParameters(Builder builder) {
            this.api = builder.api;
            this.region = builder.region;
            this.accessKey = builder.accessKey;
            this.secretKey = builder.secretKey;
            this.contentBody = builder.contentBody;
            this.requestUrl = builder.requestUrl;
            this.headerParamsMap = builder.headerParamsMap;
        }

        public static Builder builder() {
            return new Builder();
        }

        public static class Builder {
            private String api;
            private String region;
            private String accessKey;
            private String secretKey;
            private ContentBody contentBody;
            private String requestUrl;
            private Map<String, String> headerParamsMap;

            public Builder api(String api) {
                this.api = api;
                return this;
            }

            public Builder region(String region) {
                this.region = region;
                return this;
            }

            public Builder accessKey(String accessKey) {
                this.accessKey = accessKey;
                return this;
            }

            public Builder secretKey(String secretKey) {
                this.secretKey = secretKey;
                return this;
            }

            public Builder contentBody(ContentBody contentBody) {
                this.contentBody = contentBody;
                return this;
            }

            public Builder requestUrl(String requestUrl) {
                this.requestUrl = requestUrl;
                return this;
            }

            public Builder headerParamsMap(Map<String, String> headerParamsMap) {
                this.headerParamsMap = headerParamsMap;
                return this;
            }

            public HttpParameters build() {
                return new HttpParameters(this);
            }
        }

        // Getters
        public String getApi() {
            return api;
        }

        public String getRegion() {
            return region;
        }

        public String getAccessKey() {
            return accessKey;
        }

        public String getSecretKey() {
            return secretKey;
        }

        public ContentBody getContentBody() {
            return contentBody;
        }

        public String getRequestUrl() {
            return requestUrl;
        }

        public Map<String, String> getHeaderParamsMap() {
            return headerParamsMap;
        }
    }

    /**
     * 请求体内容
     */
    public static class ContentBody {
        private String content;

        public ContentBody(String content) {
            this.content = content;
        }

        public String getContent() {
            return content;
        }
    }

    /**
     * HTTP响应结果
     */
    public static class HttpReturn {
        private int statusCode;
        private String body;

        public HttpReturn(int statusCode, String body) {
            this.statusCode = statusCode;
            this.body = body;
        }

        public int getStatusCode() {
            return statusCode;
        }

        public String getBody() {
            return body;
        }

        public boolean isSuccess() {
            return statusCode >= 200 && statusCode < 300;
        }
    }

}
