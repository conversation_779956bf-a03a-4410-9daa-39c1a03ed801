package com.organ.module.auth.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 第三方（海政通）用户信息DTO
 *
 * <AUTHOR>
 */
@ApiModel("第三方用户信息")
@Data
public class HztUserInfoDTO {

    /**
     * 第三方用户ID
     */
    @ApiModelProperty("第三方用户ID")
    private String userId;

    /**
     * 用户账号
     */
    @ApiModelProperty("用户账号")
    private String account;

    /**
     * 用户昵称
     */
    @ApiModelProperty("用户昵称")
    private String nickName;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    private String phone;

    /**
     * 邮箱
     */
    @ApiModelProperty("邮箱")
    private String email;

    /**
     * 外部ID
     */
    @ApiModelProperty("外部ID")
    private String externalId;

    /**
     * 是否启用
     */
    @ApiModelProperty("是否启用")
    private Boolean enable;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private String createTime;

    /**
     * 扩展信息（JSON字符串）
     */
    @ApiModelProperty("扩展信息")
    private String extendMap;

}
