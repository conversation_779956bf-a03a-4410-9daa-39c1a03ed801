package com.organ.module.auth.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 第三方（海政通）用户信息DTO
 *
 * <AUTHOR>
 */
@ApiModel("第三方用户信息")
@Data
public class HztUserInfoDTO {

    /**
     * 第三方用户ID
     */
    @ApiModelProperty("第三方用户ID")
    private String userId;

    /**
     * 用户名
     */
    @ApiModelProperty("用户名")
    private String username;

    /**
     * 真实姓名
     */
    @ApiModelProperty("真实姓名")
    private String realName;

    /**
     * 邮箱
     */
    @ApiModelProperty("邮箱")
    private String email;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    private String phone;

    /**
     * 身份证号
     */
    @ApiModelProperty("身份证号")
    private String idCard;

    /**
     * 头像
     */
    @ApiModelProperty("头像")
    private String avatar;

    /**
     * 工号
     */
    @ApiModelProperty("工号")
    private String employeeNo;

    /**
     * 部门ID
     */
    @ApiModelProperty("部门ID")
    private String deptId;

    /**
     * 部门名称
     */
    @ApiModelProperty("部门名称")
    private String deptName;

    /**
     * 组织机构代码
     */
    @ApiModelProperty("组织机构代码")
    private String orgCode;

    /**
     * 用户状态
     */
    @ApiModelProperty("用户状态")
    private Integer status;

}
