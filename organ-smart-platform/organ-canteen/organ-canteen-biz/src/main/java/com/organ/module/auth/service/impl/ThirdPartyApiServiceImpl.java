package com.organ.module.auth.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hainancrc.framework.common.exception.ServiceException;
import com.organ.module.auth.config.ThirdPartyAuthConfig;
import com.organ.module.auth.dto.HztUserInfoDTO;
import com.organ.module.auth.dto.ThirdPartyTokenDTO;
import com.organ.module.auth.service.ThirdPartyApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 第三方API服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ThirdPartyApiServiceImpl implements ThirdPartyApiService {

    @Resource
    private ThirdPartyAuthConfig thirdPartyAuthConfig;

    @Override
    public ThirdPartyTokenDTO getThirdPartyToken(String accessToken, String clientType) {
        log.info("开始获取第三方token，accessToken: {}, clientType: {}", accessToken, clientType);
        
        // 获取客户端配置
        ThirdPartyAuthConfig.ClientConfig clientConfig = thirdPartyAuthConfig.getClientConfig(clientType);
        if (clientConfig == null) {
            throw new ServiceException("未找到客户端配置: " + clientType);
        }
        
        if (!clientConfig.getEnabled()) {
            throw new ServiceException("客户端已禁用: " + clientType);
        }

        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("accessToken", accessToken);
        params.put("appKey", clientConfig.getAppKey());
        params.put("appSecret", clientConfig.getAppSecret());

        try {
            // 发送HTTP请求
            HttpResponse response = HttpRequest.post(thirdPartyAuthConfig.getTokenApiUrl())
                    .form(params)
                    .timeout(10000)
                    .execute();

            if (!response.isOk()) {
                log.error("获取第三方token失败，HTTP状态码: {}, 响应内容: {}", response.getStatus(), response.body());
                throw new ServiceException("获取第三方token失败");
            }

            // 解析响应结果
            String responseBody = response.body();
            log.info("获取第三方token响应: {}", responseBody);
            
            JSONObject jsonResponse = JSONUtil.parseObj(responseBody);
            
            // 检查响应状态
            if (!isSuccessResponse(jsonResponse)) {
                String errorMsg = jsonResponse.getStr("message", "获取token失败");
                log.error("第三方API返回错误: {}", errorMsg);
                throw new ServiceException("获取第三方token失败: " + errorMsg);
            }

            // 解析token信息
            JSONObject data = jsonResponse.getJSONObject("data");
            if (data == null) {
                throw new ServiceException("第三方API返回数据格式错误");
            }

            ThirdPartyTokenDTO tokenDTO = new ThirdPartyTokenDTO();
            tokenDTO.setToken(data.getStr("token"));
            tokenDTO.setRefreshToken(data.getStr("refreshToken"));
            tokenDTO.setExpiresIn(data.getLong("expiresIn"));
            tokenDTO.setTokenType(data.getStr("tokenType"));

            log.info("成功获取第三方token");
            return tokenDTO;

        } catch (Exception e) {
            log.error("调用第三方token API异常", e);
            throw new ServiceException("获取第三方token失败: " + e.getMessage());
        }
    }

    @Override
    public HztUserInfoDTO getThirdPartyUserInfo(String accessToken, String token, String clientType) {
        log.info("开始获取第三方用户信息，accessToken: {}, token: {}, clientType: {}", accessToken, token, clientType);
        
        // 获取客户端配置
        ThirdPartyAuthConfig.ClientConfig clientConfig = thirdPartyAuthConfig.getClientConfig(clientType);
        if (clientConfig == null) {
            throw new ServiceException("未找到客户端配置: " + clientType);
        }

        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("accessToken", accessToken);
        params.put("token", token);
        params.put("appKey", clientConfig.getAppKey());

        try {
            // 发送HTTP请求
            HttpResponse response = HttpRequest.post(thirdPartyAuthConfig.getUserInfoApiUrl())
                    .form(params)
                    .timeout(10000)
                    .execute();

            if (!response.isOk()) {
                log.error("获取第三方用户信息失败，HTTP状态码: {}, 响应内容: {}", response.getStatus(), response.body());
                throw new ServiceException("获取第三方用户信息失败");
            }

            // 解析响应结果
            String responseBody = response.body();
            log.info("获取第三方用户信息响应: {}", responseBody);
            
            JSONObject jsonResponse = JSONUtil.parseObj(responseBody);
            
            // 检查响应状态
            if (!isSuccessResponse(jsonResponse)) {
                String errorMsg = jsonResponse.getStr("message", "获取用户信息失败");
                log.error("第三方API返回错误: {}", errorMsg);
                throw new ServiceException("获取第三方用户信息失败: " + errorMsg);
            }

            // 解析用户信息
            JSONObject data = jsonResponse.getJSONObject("data");
            if (data == null) {
                throw new ServiceException("第三方API返回数据格式错误");
            }

            HztUserInfoDTO userInfoDTO = new HztUserInfoDTO();
            userInfoDTO.setUserId(data.getStr("userId"));
            userInfoDTO.setUsername(data.getStr("username"));
            userInfoDTO.setRealName(data.getStr("realName"));
            userInfoDTO.setEmail(data.getStr("email"));
            userInfoDTO.setPhone(data.getStr("phone"));
            userInfoDTO.setIdCard(data.getStr("idCard"));
            userInfoDTO.setAvatar(data.getStr("avatar"));
            userInfoDTO.setEmployeeNo(data.getStr("employeeNo"));
            userInfoDTO.setDeptId(data.getStr("deptId"));
            userInfoDTO.setDeptName(data.getStr("deptName"));
            userInfoDTO.setOrgCode(data.getStr("orgCode"));
            userInfoDTO.setStatus(data.getInt("status"));

            log.info("成功获取第三方用户信息，用户ID: {}", userInfoDTO.getUserId());
            return userInfoDTO;

        } catch (Exception e) {
            log.error("调用第三方用户信息API异常", e);
            throw new ServiceException("获取第三方用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 判断第三方API响应是否成功
     *
     * @param jsonResponse 响应JSON对象
     * @return 是否成功
     */
    private boolean isSuccessResponse(JSONObject jsonResponse) {
        // 根据第三方API的实际响应格式调整判断逻辑
        Integer code = jsonResponse.getInt("code");
        String status = jsonResponse.getStr("status");
        
        // 常见的成功状态码：200, 0, "success", "ok"
        return (code != null && (code == 200 || code == 0)) || 
               (StrUtil.isNotBlank(status) && ("success".equalsIgnoreCase(status) || "ok".equalsIgnoreCase(status)));
    }

}
