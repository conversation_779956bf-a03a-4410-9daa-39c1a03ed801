package com.organ.module.auth.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hainancrc.framework.common.exception.ServiceException;
import com.organ.module.auth.config.ThirdPartyAuthConfig;
import com.organ.module.auth.dto.HztUserInfoDTO;
import com.organ.module.auth.dto.ThirdPartyTokenDTO;
import com.organ.module.auth.service.ThirdPartyApiService;
import com.organ.module.auth.util.HttpCaller;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 第三方API服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ThirdPartyApiServiceImpl implements ThirdPartyApiService {

    @Resource
    private ThirdPartyAuthConfig thirdPartyAuthConfig;

    @Override
    public ThirdPartyTokenDTO getThirdPartyToken(String accessToken, String clientType) {
        log.info("开始获取第三方token，accessToken: {}, clientType: {}", accessToken, clientType);

        // 获取客户端配置
        ThirdPartyAuthConfig.ClientConfig clientConfig = thirdPartyAuthConfig.getClientConfig(clientType);
        if (clientConfig == null) {
            throw new ServiceException(500, "未找到客户端配置: " + clientType);
        }

        if (!clientConfig.getEnabled()) {
            throw new ServiceException(500, "客户端已禁用: " + clientType);
        }

        // 构建请求参数JSON
        Map<String, Object> requestData = new HashMap<>();
        requestData.put("appKey", clientConfig.getAppKey());
        requestData.put("appSecret", clientConfig.getAppSecret());
        requestData.put("code", accessToken);
        requestData.put("redirectUri", "");

        String contentJson = JSONUtil.toJsonStr(requestData);

        // 构建请求头
        Map<String, String> headers = new HashMap<>();
        headers.put("Uaa-Tenant-Id", thirdPartyAuthConfig.getTenantId());

        try {
            // 构建HTTP请求参数
            HttpCaller.ContentBody contentBody = new HttpCaller.ContentBody(contentJson);
            HttpCaller.HttpParameters parameters = HttpCaller.HttpParameters.builder()
                    .api(thirdPartyAuthConfig.getTokenApi())
                    .headerParamsMap(headers)
                    .region(thirdPartyAuthConfig.getRegion())
                    .accessKey(clientConfig.getAppKey())
                    .secretKey(clientConfig.getAppSecret())
                    .contentBody(contentBody)
                    .requestUrl(thirdPartyAuthConfig.getRequestUrl())
                    .build();

            // 发送HTTP请求
            HttpCaller.HttpReturn response = HttpCaller.getInstance().call(parameters);

            if (!response.isSuccess()) {
                log.error("获取第三方token失败，HTTP状态码: {}, 响应内容: {}", response.getStatusCode(), response.getBody());
                throw new ServiceException(500, "获取第三方token失败");
            }

            // 解析响应结果
            String responseBody = response.getBody();
            log.info("获取第三方token响应: {}", responseBody);

            JSONObject jsonResponse = JSONUtil.parseObj(responseBody);

            // 检查响应状态
            if (!isSuccessResponse(jsonResponse)) {
                String errorMsg = jsonResponse.getStr("message", "获取token失败");
                log.error("第三方API返回错误: {}", errorMsg);
                throw new ServiceException(500, "获取第三方token失败: " + errorMsg);
            }

            // 解析token信息
            JSONObject data = jsonResponse.getJSONObject("data");
            if (data == null) {
                throw new ServiceException(500, "第三方API返回数据格式错误");
            }

            ThirdPartyTokenDTO tokenDTO = new ThirdPartyTokenDTO();
            tokenDTO.setAccessToken(data.getStr("accessToken"));
            tokenDTO.setRefreshToken(data.getStr("refreshToken"));
            tokenDTO.setExpiredIn(data.getLong("expiredIn"));

            log.info("成功获取第三方token");
            return tokenDTO;

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("调用第三方token API异常", e);
            throw new ServiceException(500, "获取第三方token失败: " + e.getMessage());
        }
    }

    @Override
    public HztUserInfoDTO getThirdPartyUserInfo(String accessToken, String token, String clientType) {
        log.info("开始获取第三方用户信息，accessToken: {}, token: {}, clientType: {}", accessToken, token, clientType);

        // 获取客户端配置
        ThirdPartyAuthConfig.ClientConfig clientConfig = thirdPartyAuthConfig.getClientConfig(clientType);
        if (clientConfig == null) {
            throw new ServiceException(500, "未找到客户端配置: " + clientType);
        }

        // 构建请求参数JSON（使用accessToken作为用户信息查询的token）
        Map<String, Object> requestData = new HashMap<>();
        requestData.put("accessToken", token); // 使用获取到的token

        String contentJson = JSONUtil.toJsonStr(requestData);

        // 构建请求头
        Map<String, String> headers = new HashMap<>();
        headers.put("Uaa-Tenant-Id", thirdPartyAuthConfig.getTenantId());

        try {
            // 构建HTTP请求参数
            HttpCaller.ContentBody contentBody = new HttpCaller.ContentBody(contentJson);
            HttpCaller.HttpParameters parameters = HttpCaller.HttpParameters.builder()
                    .api(thirdPartyAuthConfig.getUserInfoApi())
                    .headerParamsMap(headers)
                    .region(thirdPartyAuthConfig.getRegion())
                    .accessKey(clientConfig.getAppKey())
                    .secretKey(clientConfig.getAppSecret())
                    .contentBody(contentBody)
                    .requestUrl(thirdPartyAuthConfig.getRequestUrl())
                    .build();

            // 发送HTTP请求
            HttpCaller.HttpReturn response = HttpCaller.getInstance().call(parameters);

            if (!response.isSuccess()) {
                log.error("获取第三方用户信息失败，HTTP状态码: {}, 响应内容: {}", response.getStatusCode(), response.getBody());
                throw new ServiceException(500, "获取第三方用户信息失败");
            }

            // 解析响应结果
            String responseBody = response.getBody();
            log.info("获取第三方用户信息响应: {}", responseBody);

            JSONObject jsonResponse = JSONUtil.parseObj(responseBody);

            // 检查响应状态
            if (!isSuccessResponse(jsonResponse)) {
                String errorMsg = jsonResponse.getStr("message", "获取用户信息失败");
                log.error("第三方API返回错误: {}", errorMsg);
                throw new ServiceException(500, "获取第三方用户信息失败: " + errorMsg);
            }

            // 解析用户信息
            JSONObject data = jsonResponse.getJSONObject("data");
            if (data == null) {
                throw new ServiceException(500, "第三方API返回数据格式错误");
            }

            HztUserInfoDTO userInfoDTO = new HztUserInfoDTO();
            userInfoDTO.setUserId(data.getStr("userId"));
            userInfoDTO.setAccount(data.getStr("account"));
            userInfoDTO.setNickName(data.getStr("nickName"));
            userInfoDTO.setPhone(data.getStr("phone"));
            userInfoDTO.setEmail(data.getStr("email"));
            userInfoDTO.setExternalId(data.getStr("externalId"));
            userInfoDTO.setEnable(data.getBool("enable"));
            userInfoDTO.setCreateTime(data.getStr("createTime"));
            userInfoDTO.setExtendMap(data.getStr("extendMap"));

            log.info("成功获取第三方用户信息，用户ID: {}", userInfoDTO.getUserId());
            return userInfoDTO;

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("调用第三方用户信息API异常", e);
            throw new ServiceException(500, "获取第三方用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 判断第三方API响应是否成功
     *
     * @param jsonResponse 响应JSON对象
     * @return 是否成功
     */
    private boolean isSuccessResponse(JSONObject jsonResponse) {
        // 根据第三方API的实际响应格式调整判断逻辑
        Boolean success = jsonResponse.getBool("success");
        return success != null && success;
    }

}
