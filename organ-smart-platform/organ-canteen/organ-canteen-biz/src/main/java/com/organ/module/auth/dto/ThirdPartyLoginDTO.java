package com.organ.module.auth.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 第三方登录请求DTO
 *
 * <AUTHOR>
 */
@ApiModel("第三方登录请求")
@Data
public class ThirdPartyLoginDTO {

    /**
     * 第三方认证中心返回的accessToken
     */
    @ApiModelProperty(value = "第三方认证中心返回的accessToken", required = true)
    @NotBlank(message = "accessToken不能为空")
    private String accessToken;

    /**
     * 客户端类型（用于区分不同的AK、SK配置）
     * 可选值：user（用户端）、operation（运营端）、admin（管理端）
     */
    @ApiModelProperty(value = "客户端类型", required = true, example = "user")
    @NotBlank(message = "客户端类型不能为空")
    private String clientType;

}
