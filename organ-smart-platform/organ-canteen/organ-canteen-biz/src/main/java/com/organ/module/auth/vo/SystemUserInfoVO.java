package com.organ.module.auth.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 智慧食堂用户信息VO
 *
 * <AUTHOR>
 */
@ApiModel("智慧食堂用户信息")
@Data
public class SystemUserInfoVO {

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    private Long id;

    /**
     * 用户名
     */
    @ApiModelProperty("用户名")
    private String username;

    /**
     * 真实姓名
     */
    @ApiModelProperty("真实姓名")
    private String realName;

    /**
     * 邮箱
     */
    @ApiModelProperty("邮箱")
    private String email;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    private String phone;

    /**
     * 头像
     */
    @ApiModelProperty("头像")
    private String avatar;

    /**
     * 工号
     */
    @ApiModelProperty("工号")
    private String employeeNo;

    /**
     * 部门ID
     */
    @ApiModelProperty("部门ID")
    private Long deptId;

    /**
     * 部门名称
     */
    @ApiModelProperty("部门名称")
    private String deptName;

    /**
     * 用户状态
     */
    @ApiModelProperty("用户状态")
    private Integer status;

    /**
     * 余额
     */
    @ApiModelProperty("余额")
    private Double balance;

    /**
     * 消费总额
     */
    @ApiModelProperty("消费总额")
    private Double totalConsumption;

    /**
     * 会员等级
     */
    @ApiModelProperty("会员等级")
    private String memberLevel;

}
