package com.organ.module.common.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hainancrc.framework.common.exception.ServiceException;
import com.hainancrc.framework.redis.service.RedisService;
import com.organ.module.auth.dto.HztUserInfoDTO;
import com.organ.module.auth.dto.ThirdPartyTokenDTO;
import com.organ.module.auth.service.ThirdPartyApiService;
import com.organ.module.auth.vo.SystemUserInfoVO;
import com.organ.module.canteen.user.entity.CanteenUserDO;
import com.organ.module.canteen.user.mapper.CanteenUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import java.util.concurrent.TimeUnit;

/**
 * 系统用户工具类
 * 提供获取当前登录用户的方法
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SystemUserUtil {

    @Resource
    private RedisService redisService;

    @Resource
    private CanteenUserMapper canteenUserMapper;

    @Resource
    private ThirdPartyApiService thirdPartyApiService;

    /**
     * Redis中存储用户信息的key前缀
     */
    private static final String USER_CACHE_KEY_PREFIX = "canteenRk-";

    /**
     * 获取当前登录用户信息
     * 优先从Redis中获取，如果没有则通过第三方API获取
     *
     * @return 当前登录用户信息
     * @throws ServiceException 如果用户未登录或token已过期
     */
    public static SystemUserInfoVO getCurrentUser() {
        return getCurrentUser(false);
    }

    /**
     * 获取当前登录用户信息
     *
     * @param refreshFromThirdParty 是否强制从第三方API刷新用户信息
     * @return 当前登录用户信息
     * @throws ServiceException 如果用户未登录或token已过期
     */
    public static SystemUserInfoVO getCurrentUser(boolean refreshFromThirdParty) {
        // 获取当前请求的token
        String token = getCurrentToken();
        if (StrUtil.isBlank(token)) {
            throw new ServiceException(401, "用户未登录");
        }

        return SpringContextUtil.getBean(SystemUserUtil.class).getUserInfo(token, refreshFromThirdParty);
    }

    /**
     * 获取当前用户ID
     *
     * @return 当前用户ID
     */
    public static Long getCurrentUserId() {
        SystemUserInfoVO userInfo = getCurrentUser();
        return userInfo.getId();
    }

    /**
     * 获取当前用户名
     *
     * @return 当前用户名
     */
    public static String getCurrentUsername() {
        SystemUserInfoVO userInfo = getCurrentUser();
        return userInfo.getUsername();
    }

    /**
     * 检查用户是否已登录
     *
     * @return 是否已登录
     */
    public static boolean isLoggedIn() {
        try {
            String token = getCurrentToken();
            if (StrUtil.isBlank(token)) {
                return false;
            }

            SystemUserUtil util = SpringContextUtil.getBean(SystemUserUtil.class);
            String cacheKey = USER_CACHE_KEY_PREFIX + token;
            return util.redisService.hasKey(cacheKey);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取用户信息（内部方法）
     *
     * @param token                 用户token
     * @param refreshFromThirdParty 是否强制从第三方API刷新
     * @return 用户信息
     */
    public SystemUserInfoVO getUserInfo(String token, boolean refreshFromThirdParty) {
        String cacheKey = USER_CACHE_KEY_PREFIX + token;

        // 如果不强制刷新，先尝试从Redis获取
        if (!refreshFromThirdParty) {
            String cachedUserInfo = redisService.getCacheObject(cacheKey);
            if (StrUtil.isNotBlank(cachedUserInfo)) {
                try {
                    SystemUserInfoVO userInfo = JSONUtil.toBean(cachedUserInfo, SystemUserInfoVO.class);
                    log.debug("从Redis获取用户信息成功，用户ID: {}", userInfo.getId());

                    // 刷新Redis过期时间
                    refreshTokenExpiration(cacheKey);
                    return userInfo;
                } catch (Exception e) {
                    log.warn("解析Redis中的用户信息失败", e);
                }
            }
        }

        // 从Redis获取失败或强制刷新，尝试通过第三方API获取
        return refreshUserInfoFromThirdParty(token, cacheKey);
    }

    /**
     * 从第三方API刷新用户信息
     *
     * @param token    用户token
     * @param cacheKey Redis缓存key
     * @return 用户信息
     */
    private SystemUserInfoVO refreshUserInfoFromThirdParty(String token, String cacheKey) {
        try {
            // 从Redis获取第三方token信息
            String thirdPartyTokenInfo = redisService.getCacheObject(cacheKey + ":thirdPartyToken");
            if (StrUtil.isBlank(thirdPartyTokenInfo)) {
                throw new ServiceException(401, "第三方token信息不存在，请重新登录");
            }

            ThirdPartyTokenDTO thirdPartyToken = JSONUtil.toBean(thirdPartyTokenInfo, ThirdPartyTokenDTO.class);

            // 从Redis获取accessToken和clientType
            String accessToken = redisService.getCacheObject(cacheKey + ":accessToken");
            String clientType = redisService.getCacheObject(cacheKey + ":clientType");

            if (StrUtil.isBlank(accessToken) || StrUtil.isBlank(clientType)) {
                throw new ServiceException(401, "登录信息不完整，请重新登录");
            }

            // 调用第三方API获取用户信息
            HztUserInfoDTO hztUserInfo = thirdPartyApiService.getThirdPartyUserInfo(
                    accessToken, thirdPartyToken.getAccessToken(), clientType);

            // 查询本地用户信息
            CanteenUserDO canteenUser = canteenUserMapper.selectByThirdPartyUserId(hztUserInfo.getUserId());
            if (canteenUser == null) {
                throw new ServiceException(401, "用户信息不存在，请重新登录");
            }

            // 更新用户信息（简化处理，不更新最后登录时间）

            // 转换为SystemUserInfoVO
            SystemUserInfoVO userInfo = convertToSystemUserInfo(canteenUser);

            // 更新Redis缓存
            redisService.setCacheObject(cacheKey, JSONUtil.toJsonStr(userInfo),
                    thirdPartyToken.getExpiredIn(), TimeUnit.SECONDS);

            log.info("从第三方API刷新用户信息成功，用户ID: {}", userInfo.getId());
            return userInfo;

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("从第三方API获取用户信息失败", e);
            throw new ServiceException(401, "获取用户信息失败，请重新登录");
        }
    }

    /**
     * 刷新token过期时间
     *
     * @param cacheKey Redis缓存key
     */
    private void refreshTokenExpiration(String cacheKey) {
        try {
            // 获取第三方token信息以确定过期时间
            String thirdPartyTokenInfo = redisService.getCacheObject(cacheKey + ":thirdPartyToken");
            if (StrUtil.isNotBlank(thirdPartyTokenInfo)) {
                ThirdPartyTokenDTO thirdPartyToken = JSONUtil.toBean(thirdPartyTokenInfo, ThirdPartyTokenDTO.class);
                redisService.expire(cacheKey, thirdPartyToken.getExpiredIn(), TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            log.warn("刷新token过期时间失败", e);
        }
    }

    /**
     * 获取当前请求的token
     *
     * @return token
     */
    private static String getCurrentToken() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder
                    .getRequestAttributes();
            if (attributes == null) {
                return null;
            }

            HttpServletRequest request = attributes.getRequest();

            // 从Header中获取token
            String token = request.getHeader("Authorization");
            if (StrUtil.isNotBlank(token) && token.startsWith("Bearer ")) {
                return token.substring(7);
            }

            // 从Header中获取token（不带Bearer前缀）
            token = request.getHeader("token");
            if (StrUtil.isNotBlank(token)) {
                return token;
            }

            // 从参数中获取token
            token = request.getParameter("token");
            return token;

        } catch (Exception e) {
            log.warn("获取当前请求token失败", e);
            return null;
        }
    }

    /**
     * 将CanteenUserDO转换为SystemUserInfoVO
     *
     * @param canteenUser 智慧食堂用户实体
     * @return 系统用户信息VO
     */
    private SystemUserInfoVO convertToSystemUserInfo(CanteenUserDO canteenUser) {
        SystemUserInfoVO userInfo = new SystemUserInfoVO();

        userInfo.setId(canteenUser.getId());
        userInfo.setUsername(canteenUser.getUsername());
        userInfo.setPhone(canteenUser.getMobile());

        // 从第三方用户数据JSON中解析信息
        if (StrUtil.isNotBlank(canteenUser.getThirdPartyUserData())) {
            try {
                HztUserInfoDTO hztUserInfo = JSONUtil.toBean(canteenUser.getThirdPartyUserData(), HztUserInfoDTO.class);
                userInfo.setRealName(hztUserInfo.getNickName());
                userInfo.setEmail(hztUserInfo.getEmail());
                // 可以根据需要从extendMap中解析更多信息
            } catch (Exception e) {
                log.warn("解析第三方用户数据失败", e);
            }
        }

        // 状态转换
        if ("ACTIVE".equals(canteenUser.getStatus())) {
            userInfo.setStatus(1);
        } else {
            userInfo.setStatus(0);
        }

        userInfo.setBalance(canteenUser.getBalance() != null ? canteenUser.getBalance().doubleValue() : 0.0);
        userInfo.setTotalConsumption(0.0); // 简化处理
        userInfo.setMemberLevel("普通会员"); // 简化处理

        return userInfo;
    }

}
