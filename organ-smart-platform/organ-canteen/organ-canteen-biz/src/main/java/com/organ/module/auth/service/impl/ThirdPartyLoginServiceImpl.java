package com.organ.module.auth.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.hainancrc.framework.common.exception.ServiceException;
import com.hainancrc.framework.redis.service.RedisService;
import com.organ.module.auth.dto.HztUserInfoDTO;
import com.organ.module.auth.dto.ThirdPartyLoginDTO;
import com.organ.module.auth.dto.ThirdPartyTokenDTO;
import com.organ.module.auth.service.ThirdPartyApiService;
import com.organ.module.auth.service.ThirdPartyLoginService;
import com.organ.module.auth.service.UserMatchingService;
import com.organ.module.auth.vo.SystemUserInfoVO;
import com.organ.module.auth.vo.ThirdPartyLoginVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 第三方登录服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ThirdPartyLoginServiceImpl implements ThirdPartyLoginService {

    @Resource
    private ThirdPartyApiService thirdPartyApiService;

    @Resource
    private UserMatchingService userMatchingService;

    @Resource
    private RedisService redisService;

    /**
     * Redis中存储用户信息的key前缀
     */
    private static final String USER_CACHE_KEY_PREFIX = "canteenRk-";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ThirdPartyLoginVO loginByAccessToken(ThirdPartyLoginDTO loginDTO) {
        log.info("开始第三方登录，accessToken: {}, clientType: {}", loginDTO.getAccessToken(), loginDTO.getClientType());

        try {
            // 1. 通过SK+accessToken调用第三方提供的获取登录用户token开放API
            ThirdPartyTokenDTO thirdPartyToken = thirdPartyApiService.getThirdPartyToken(
                    loginDTO.getAccessToken(), loginDTO.getClientType());

            if (thirdPartyToken == null || thirdPartyToken.getAccessToken() == null) {
                throw new ServiceException(500, "获取第三方token失败");
            }

            log.info("获取第三方token成功，token: {}, expiredIn: {}",
                    thirdPartyToken.getAccessToken(), thirdPartyToken.getExpiredIn());

            // 2. 通过accessToken+Token调用第三方提供的获取登录用户信息开放API
            HztUserInfoDTO hztUserInfo = thirdPartyApiService.getThirdPartyUserInfo(
                    loginDTO.getAccessToken(), thirdPartyToken.getAccessToken(), loginDTO.getClientType());

            if (hztUserInfo == null || hztUserInfo.getUserId() == null) {
                throw new ServiceException(500, "获取第三方用户信息失败");
            }

            log.info("获取第三方用户信息成功，用户ID: {}, 昵称: {}",
                    hztUserInfo.getUserId(), hztUserInfo.getNickName());

            // 3. 通过匹配措施，将hztUserInfo对应到当前智慧食堂的用户信息
            SystemUserInfoVO systemUserInfo = userMatchingService.matchUser(hztUserInfo);

            if (systemUserInfo == null || systemUserInfo.getId() == null) {
                throw new ServiceException(500, "用户匹配失败");
            }

            log.info("用户匹配成功，智慧食堂用户ID: {}, 用户名: {}",
                    systemUserInfo.getId(), systemUserInfo.getUsername());

            // 4. 生成系统token
            String systemToken = generateSystemToken();

            // 5. 将"canteenRk-" + token: systemUserInfo键值对存储到redis中
            String cacheKey = USER_CACHE_KEY_PREFIX + systemToken;

            // 存储用户信息
            redisService.setCacheObject(cacheKey, JSONUtil.toJsonStr(systemUserInfo),
                    thirdPartyToken.getExpiredIn(), TimeUnit.SECONDS);

            // 存储第三方token信息（用于后续刷新用户信息）
            redisService.setCacheObject(cacheKey + ":thirdPartyToken", JSONUtil.toJsonStr(thirdPartyToken),
                    thirdPartyToken.getExpiredIn(), TimeUnit.SECONDS);

            // 存储accessToken和clientType（用于后续刷新用户信息）
            redisService.setCacheObject(cacheKey + ":accessToken", loginDTO.getAccessToken(),
                    thirdPartyToken.getExpiredIn(), TimeUnit.SECONDS);
            redisService.setCacheObject(cacheKey + ":clientType", loginDTO.getClientType(),
                    thirdPartyToken.getExpiredIn(), TimeUnit.SECONDS);

            log.info("用户信息已存储到Redis，cacheKey: {}, expiredIn: {}", cacheKey, thirdPartyToken.getExpiredIn());

            // 6. 构建返回结果
            ThirdPartyLoginVO loginVO = new ThirdPartyLoginVO();
            loginVO.setToken(systemToken);
            loginVO.setRefreshToken(thirdPartyToken.getRefreshToken());
            loginVO.setUserInfo(systemUserInfo);
            loginVO.setExpiresIn(thirdPartyToken.getExpiredIn());

            log.info("第三方登录成功，用户ID: {}, token: {}", systemUserInfo.getId(), systemToken);
            return loginVO;

        } catch (ServiceException e) {
            log.error("第三方登录失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("第三方登录异常", e);
            throw new ServiceException(500, "登录失败: " + e.getMessage());
        }
    }

    /**
     * 生成系统token
     *
     * @return 系统token
     */
    private String generateSystemToken() {
        // 使用UUID生成唯一token
        return IdUtil.fastSimpleUUID();
    }

}
