package com.organ.module.auth.service;

import com.organ.module.auth.dto.HztUserInfoDTO;
import com.organ.module.auth.dto.ThirdPartyTokenDTO;

/**
 * 第三方API服务接口
 * 用于调用第三方登录系统的开放API
 *
 * <AUTHOR>
 */
public interface ThirdPartyApiService {

    /**
     * 通过SK+accessToken获取第三方系统的token信息
     *
     * @param accessToken 第三方认证中心返回的accessToken
     * @param clientType  客户端类型（用于获取对应的SK）
     * @return 第三方token信息
     */
    ThirdPartyTokenDTO getThirdPartyToken(String accessToken, String clientType);

    /**
     * 通过accessToken+Token获取第三方用户信息
     *
     * @param accessToken 第三方认证中心返回的accessToken
     * @param token       第三方系统token
     * @param clientType  客户端类型
     * @return 第三方用户信息
     */
    HztUserInfoDTO getThirdPartyUserInfo(String accessToken, String token, String clientType);

}
