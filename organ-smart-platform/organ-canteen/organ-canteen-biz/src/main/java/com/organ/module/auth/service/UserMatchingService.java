package com.organ.module.auth.service;

import com.organ.module.auth.dto.HztUserInfoDTO;
import com.organ.module.auth.vo.SystemUserInfoVO;

/**
 * 用户匹配服务接口
 * 用于实现第三方用户信息与智慧食堂用户信息的匹配逻辑
 *
 * <AUTHOR>
 */
public interface UserMatchingService {

    /**
     * 将第三方用户信息匹配到智慧食堂用户信息
     * 匹配规则：
     * 1. 优先通过第三方用户ID匹配
     * 2. 通过工号匹配
     * 3. 通过手机号匹配
     * 4. 通过身份证号匹配
     * 5. 如果都匹配不到，则创建新用户
     *
     * @param hztUserInfo 第三方用户信息
     * @return 智慧食堂用户信息
     */
    SystemUserInfoVO matchUser(HztUserInfoDTO hztUserInfo);

    /**
     * 更新用户的第三方关联信息
     *
     * @param userId          智慧食堂用户ID
     * @param thirdPartyUserId 第三方用户ID
     * @param hztUserInfo     第三方用户信息
     */
    void updateUserThirdPartyInfo(Long userId, String thirdPartyUserId, HztUserInfoDTO hztUserInfo);

}
