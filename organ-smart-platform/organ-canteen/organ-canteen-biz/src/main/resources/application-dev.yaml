server:
  port: 8180
  servlet:
    context-path: /newcorp
--- #################### 数据库相关配置 ####################

spring:
  # 数据源配置项
  autoconfigure:
    exclude:
      - com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure # 排除 Druid 的自动配置，使用 dynamic-datasource-spring-boot-starter 配置多数据源
  datasource:
    druid: # Druid 【监控】相关的全局配置
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        allow: # 设置白名单，不填则允许所有访问
        url-pattern: /druid/*
        login-username: admin # 控制台管理用户名和密码
        login-password: admin_!@#qwe
      filter:
        stat:
          enabled: true
          log-slow-sql: true # 慢 SQL 记录
          slow-sql-millis: 100
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
    dynamic: # 多数据源配置
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 5 # 初始连接数
        min-idle: 10 # 最小连接池数量
        max-active: 20 # 最大连接池数量
        max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        validation-query: SELECT 1 # 配置检测连接是否有效
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
      primary: master
      datasource:
        master:
          name: hncrc_user
          url: ****************************************************************************************
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: root
          password: Jfi$2f#45@R
  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  redis:
    database: 0
    host: *************
    port: 6379
    timeout: 10000

# 日志文件配置
logging:
  file:
    name: ${user.home}/logs/${spring.application.name}.log # 日志文件名，全路径
  level:
    # 配置自己写的 MyBatis Mapper 打印日志
    com.organ.module.verify.mapper: debug
    com.organ.module.corp.mapper: debug

--- #########注册中心##########
eureka:
  client:
    service-url:
      defaultZone: http://*************:8888/eureka/
      #defaultZone: http://127.0.0.1:80/eureka
    registry-fetch-interval-seconds: 30
    register-with-eureka: true
  instance:
    prefer-ip-address: true
    lease-expiration-duration-in-seconds: 30
    lease-renewal-interval-in-seconds: 30
# 钉钉运营群通知token
dingding:
  yysj:
    token: 3ef64956cb96e7eaad5b72d019957d9457ba701a0060b7061a90e7c8b135b08f

# 第三方认证配置
third-party:
  auth:
    base-url: https://api-one.digitalhainan.com.cn/apione
    token-api: uaaOauthTokenTest
    user-info-api: uaaUserInfoTest
    region: INTER
    tenant-id: "1"
    clients:
      user:
        app-key: ${THIRD_PARTY_USER_AK:your-user-app-key}
        app-secret: ${THIRD_PARTY_USER_SK:your-user-app-secret}
        client-name: 用户端
        enabled: true
      operation:
        app-key: ${THIRD_PARTY_OPERATION_AK:your-operation-app-key}
        app-secret: ${THIRD_PARTY_OPERATION_SK:your-operation-app-secret}
        client-name: 运营端
        enabled: true
      admin:
        app-key: ${THIRD_PARTY_ADMIN_AK:your-admin-app-key}
        app-secret: ${THIRD_PARTY_ADMIN_SK:your-admin-app-secret}
        client-name: 管理端
        enabled: true
